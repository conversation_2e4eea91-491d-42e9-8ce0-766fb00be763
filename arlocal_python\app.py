"""
ArLocal Python Implementation
A Flask-based reimplementation of the ArLocal Arweave local testnet
"""

from flask import Flask, request, jsonify, Response
from flask_cors import CORS
import json
import time
import random
import string
import re
from typing import Dict, List, Optional, Any
import logging
import os
from config import config
from database import ArLocalDatabase
from utils import (
    random_id, validate_txid, validate_address, calculate_price,
    save_transaction_data, load_transaction_data, delete_transaction_data,
    format_transaction_for_response, format_block_for_response,
    calculate_transaction_id, b64url_to_bytes, bytes_to_b64url
)
from graphql_schema import schema

# Get configuration
config_name = os.environ.get('ARLOCAL_CONFIG', 'default')
app_config = config[config_name]

# Configure logging
logging.basicConfig(level=getattr(logging, app_config.LOG_LEVEL))
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config.from_object(app_config)
CORS(app, origins=app_config.CORS_ORIGINS)

# Initialize database
db = ArLocalDatabase(
    db_path=app_config.DATABASE_PATH,
    data_dir=app_config.DATA_DIR
)

# Network configuration
network_config = {
    "network": app_config.NETWORK_NAME,
    "version": app_config.NETWORK_VERSION,
    "release": app_config.NETWORK_RELEASE,
    "peers": 1,
    "node_state_latency": 0
}

# In-memory logs (could be moved to database if needed)
logs = []

def format_balance(balance: float) -> str:
    """Format balance as integer if it's a whole number, otherwise as float"""
    if balance == int(balance):
        return str(int(balance))
    return str(balance)

# Status and Info Routes
@app.route('/', methods=['GET'])
@app.route('/info', methods=['GET'])
def get_status():
    """Get network status information"""
    network_info = db.get_network_info()
    return jsonify({
        **network_config,
        **network_info
    })

@app.route('/peers', methods=['GET'])
def get_peers():
    """Get peer information"""
    host = request.headers.get('Host', 'localhost:1984')
    return jsonify([host])

@app.route('/logs', methods=['GET'])
def get_logs():
    """Get application logs"""
    try:
        # In the original, this reads from a logs file
        # For simplicity, we'll return our in-memory logs
        log_content = '\n'.join(logs) if logs else 'No logs available'
        return Response(log_content, mimetype='text/plain')
    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/reset', methods=['GET'])
def reset_network():
    """Reset the network state"""
    try:
        # Reset the network state using database
        result = db.reset_network(network_config)
        logs.append(f"Network reset at {time.time()}")
        return Response('reset done', mimetype='text/plain')
    except Exception as e:
        logger.error(f"Error resetting network: {e}")
        return jsonify({"error": str(e)}), 500

# Transaction Anchor Route
@app.route('/tx_anchor', methods=['GET'])
def get_tx_anchor():
    """Get transaction anchor (latest block ID)"""
    latest_block = db.get_latest_block()
    anchor = latest_block['id'] if latest_block else ''
    return Response(anchor, mimetype='text/plain')

# Price Calculation Route
@app.route('/price/<int:bytes_size>', methods=['GET'])
@app.route('/price/<int:bytes_size>/<address>', methods=['GET'])
def get_price(bytes_size: int, address: str = None):
    """Calculate transaction price based on data size"""
    price = calculate_price(bytes_size, app_config.DEFAULT_PRICE_PER_KB)
    return Response(str(price), mimetype='text/plain')

# Transaction Routes
@app.route('/tx/pending', methods=['GET'])
def get_pending_transactions():
    """Get list of pending transaction IDs"""
    return jsonify(db.get_pending_transactions())

@app.route('/tx/<txid>', methods=['GET'])
def get_transaction(txid: str):
    """Get transaction by ID"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found"}), 404

    logs.append(f"Retrieved transaction: {txid}")
    return jsonify(transaction)

@app.route('/tx/<txid>/status', methods=['GET'])
def get_transaction_status(txid: str):
    """Get transaction status"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found !"}), 404

    if 'block' not in transaction or not transaction['block']:
        return jsonify({"status": "Pending"})

    # Get current network height for confirmations
    network_info = db.get_network_info()
    current_height = network_info['height']

    # Return status information
    return jsonify({
        "block_indep_hash": transaction['block'],
        "block_height": transaction.get('height', 0),
        "number_of_confirmations": current_height - transaction.get('height', 0)
    })

@app.route('/tx/<txid>/offset', methods=['GET'])
def get_transaction_offset(txid: str):
    """Get transaction offset information"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found"}), 404

    data_size = int(transaction.get('data_size', 0))

    # Simplified offset calculation
    offset = random.randint(1000, 10000)  # Mock offset

    return jsonify({
        "offset": str(offset + data_size - 1),
        "size": str(data_size)
    })

@app.route('/tx/<txid>/data', methods=['GET'])
def get_transaction_raw_data(txid: str):
    """Get raw transaction data as base64url encoded string"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not found"}), 404

    # Check for the data_size limit (same as original ArLocal)
    data_size = int(transaction.get('data_size', 0))
    if data_size > 12000000:  # 12MB limit
        return Response('tx_data_too_big', mimetype='text/plain'), 400

    # Load data from file or reconstruct from chunks
    data = get_transaction_data(transaction, txid)
    if data is not None:
        # Convert binary data to base64url encoded string
        if isinstance(data, bytes):
            b64url_data = bytes_to_b64url(data)
        else:
            # If data is already a string (base64url), return as-is
            b64url_data = str(data)

        return Response(b64url_data, mimetype='text/plain')

    return jsonify({"status": 404, "error": "Data not found"}), 404

@app.route('/tx/<txid>/data.<ext>', methods=['GET'])
def get_transaction_data_with_extension(txid: str, ext: str):
    """Get transaction data with specific extension"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not found"}), 404

    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        # Determine content type based on extension
        content_type = 'application/octet-stream'
        if ext == 'json':
            content_type = 'application/json'
        elif ext == 'html':
            content_type = 'text/html'
        elif ext == 'txt':
            content_type = 'text/plain'

        return Response(data, mimetype=content_type)

    return jsonify({"status": 404, "error": "Data not found"}), 404

# Transaction field and file routes
VALID_FIELDS = ['id', 'last_tx', 'owner', 'tags', 'target', 'quantity', 'data_root', 'data_size', 'reward', 'signature']

@app.route('/tx/<txid>/<field>', methods=['GET'])
def get_transaction_field(txid: str, field: str):
    """Get specific transaction field"""
    # Check if it's a file request (contains a dot)
    if '.' in field:
        return get_transaction_file(txid, field)

    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    if field not in VALID_FIELDS:
        return jsonify({"status": 404, "error": "Field Not Found !"}), 404

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found !"}), 404

    if field in transaction:
        return Response(str(transaction[field]), mimetype='text/plain')

    return jsonify({"status": 404, "error": "Field not found in transaction"}), 404

def get_transaction_file(txid: str, filename: str):
    """Get transaction file (helper function)"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found !"}), 404

    if 'block' not in transaction or not transaction['block']:
        return Response('Pending', mimetype='text/plain')

    # Return the data if available
    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        return Response(data, mimetype='application/octet-stream')

    return jsonify({"status": 404, "error": "File not found"}), 404

@app.route('/tx', methods=['POST'])
def post_transaction():
    """Submit a new transaction"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": 400, "error": "Invalid JSON data"}), 400

        # Basic validation
        required_fields = ['owner', 'target', 'quantity', 'reward', 'last_tx', 'tags', 'signature']
        for field in required_fields:
            if field not in data:
                return jsonify({"status": 400, "error": f"Missing required field: {field}"}), 400

        # Calculate transaction ID from signature (as per Arweave protocol)
        if 'signature' not in data or not data['signature']:
            return jsonify({"status": 400, "error": "Signature is required for transaction ID calculation"}), 400

        txid = calculate_transaction_id(data['signature'])

        # If transaction ID is provided in the request, validate it matches the calculated one
        if 'id' in data and data['id'] != txid:
            return jsonify({"status": 400, "error": "Transaction ID does not match signature hash"}), 400

        # Calculate reward based on data size
        data_size = int(data.get('data_size', 0))
        calculated_reward = calculate_price(data_size, app_config.DEFAULT_PRICE_PER_KB)

        # Check if owner has enough balance
        owner_address = data.get('owner', '')
        current_balance = db.get_wallet_balance(owner_address)

        if current_balance < calculated_reward:
            # Create wallet with sufficient balance if it doesn't exist
            if current_balance == 0:
                db.create_wallet(owner_address, calculated_reward * 10)
            else:
                return jsonify({"code": 410, "msg": "You don't have enough tokens"}), 410

        # Prepare transaction data
        transaction_data = {
            "id": txid,
            "owner": data['owner'],
            "target": data['target'],
            "quantity": data['quantity'],
            "reward": data['reward'],
            "last_tx": data['last_tx'],
            "tags": data['tags'],
            "signature": data['signature'],
            "data_size": data_size,
            "data_root": data.get('data_root', ''),
            "format": data.get('format', 2),
            "owner_address": owner_address
        }

        # Store transaction data if provided (for small transactions with embedded data)
        # For large transactions, data will be uploaded as chunks separately
        if 'data' in data and data['data']:
            # Data comes in as base64url encoded string, decode it for storage
            try:
                decoded_data = b64url_to_bytes(data['data'])
                save_transaction_data(db.data_dir, txid, decoded_data)
            except Exception as e:
                logger.error(f"Error decoding transaction data: {e}")
                # Store as-is if decoding fails
                save_transaction_data(db.data_dir, txid, data['data'])
        elif data.get('data_root') and data.get('data_size'):
            # Large transaction - data will be uploaded as chunks
            # Validate that data_root and data_size are provided for chunk-based transactions
            logger.info(f"Transaction {txid} expects data to be uploaded as chunks (data_root: {data['data_root']}, data_size: {data['data_size']})")

        # Insert transaction into database
        if db.insert_transaction(transaction_data):
            # Deduct fee from wallet balance
            fee = max(int(data.get('reward', 0)), calculated_reward)
            new_balance = db.get_wallet_balance(owner_address) - fee
            db.update_wallet_balance(owner_address, new_balance)

            logs.append(f"Transaction submitted: {txid}")
            return Response(txid, mimetype='text/plain')
        else:
            return jsonify({"status": 500, "error": "Failed to insert transaction"}), 500

    except Exception as e:
        logger.error(f"Error posting transaction: {e}")
        return jsonify({"status": 500, "error": str(e)}), 500

@app.route('/tx/<txid>', methods=['DELETE'])
def delete_transaction(txid: str):
    """Delete a transaction"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not found"}), 404

    # Delete transaction from database
    if db.delete_transaction(txid):
        logs.append(f"Transaction deleted: {txid}")
        return jsonify({"status": 200, "message": "Transaction deleted"})
    else:
        return jsonify({"status": 500, "error": "Failed to delete transaction"}), 500

# Block Routes
@app.route('/block/hash/<indep_hash>', methods=['GET'])
def get_block_by_hash(indep_hash: str):
    """Get block by independent hash"""
    block = db.get_block_by_hash(indep_hash)
    if not block:
        return jsonify({"status": 404, "error": "Block not found"}), 404

    return jsonify({
        "indep_hash": block['id'],
        "timestamp": block.get('timestamp', block.get('mined_at', 0) // 1000),
        "previous_block": block['previous_block'],
        "height": block['height'],
        "txs": block['txs']
    })

@app.route('/block/height/<int:height>', methods=['GET'])
def get_block_by_height(height: int):
    """Get block by height"""
    block = db.get_block_by_height(height)
    if not block:
        return jsonify({"status": 404, "error": "Block not found"}), 404

    return jsonify({
        "indep_hash": block['id'],
        "timestamp": block.get('timestamp', block.get('mined_at', 0) // 1000),
        "previous_block": block['previous_block'],
        "height": block['height'],
        "txs": block['txs']
    })

# Mining Routes
@app.route('/mine', methods=['GET'])
@app.route('/mine/<int:qty>', methods=['GET'])
def mine_blocks(qty: int = 1):
    """Mine blocks"""
    try:
        # Mine blocks using database
        success, new_block_ids = db.mine_blocks(qty)

        if success:
            logs.append(f"Mined {qty} blocks: {new_block_ids}")
            # Return updated network info
            network_info = db.get_network_info()
            return jsonify({
                **network_config,
                **network_info
            })
        else:
            return jsonify({"error": "Failed to mine blocks"}), 500

    except Exception as e:
        logger.error(f"Error mining blocks: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/mineWithFails', methods=['GET'])
@app.route('/mineWithFails/<int:qty>', methods=['GET'])
def mine_blocks_with_fails(qty: int = 1):
    """Mine blocks with failure simulation"""
    try:
        # Simulate failure rate (from config)
        fail_rate = app_config.DEFAULT_MINE_FAIL_RATE

        # Get pending transactions and simulate failures
        pending_txs = db.get_pending_transactions()
        successful_txs = []

        for txid in pending_txs:
            if random.random() >= fail_rate:  # Transaction succeeds
                successful_txs.append(txid)
            else:  # Transaction fails - remove it
                db.delete_transaction(txid)

        # For simplicity, just mine normally with successful transactions
        # In a real implementation, you'd want to modify the mine_blocks method
        # to accept a specific list of transactions
        success, new_block_ids = db.mine_blocks(qty)

        if success:
            logs.append(f"Mined {qty} blocks with {fail_rate*100}% fail rate")
            # Return updated network info
            network_info = db.get_network_info()
            return jsonify({
                **network_config,
                **network_info
            })
        else:
            return jsonify({"error": "Failed to mine blocks"}), 500

    except Exception as e:
        logger.error(f"Error mining blocks with fails: {e}")
        return jsonify({"error": str(e)}), 500

# Wallet Routes
@app.route('/wallet', methods=['POST'])
def create_wallet():
    """Create a new wallet"""
    try:
        data = request.get_json() or {}

        # Generate address if not provided
        address = data.get('address', random_id(43))

        # Validate address format
        if not validate_address(address):
            return jsonify({"status": 422, "error": "Address badly formatted"}), 422

        # Create wallet in database
        balance = float(data.get('balance', 0))
        if db.create_wallet(address, balance):
            wallet = {
                "address": address,
                "balance": balance
            }
            logs.append(f"Wallet created: {address}")
            return jsonify(wallet)
        else:
            return jsonify({"error": "Failed to create wallet"}), 500

    except Exception as e:
        logger.error(f"Error creating wallet: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/wallet/<address>/balance', methods=['GET'])
def get_wallet_balance_route(address: str):
    """Get wallet balance"""
    balance = db.get_wallet_balance(address)
    return Response(format_balance(balance), mimetype='text/plain')

@app.route('/wallet/<address>/balance', methods=['PATCH'])
def update_wallet_balance_route(address: str):
    """Update wallet balance"""
    try:
        if not validate_address(address):
            return jsonify({"status": 422, "error": "Address badly formatted"}), 422

        data = request.get_json()
        if not data or 'balance' not in data:
            return jsonify({"status": 422, "error": "Balance is required !"}), 422

        balance = float(data['balance'])
        if db.update_wallet_balance(address, balance):
            logs.append(f"Wallet balance updated: {address} -> {balance}")
            return jsonify(data)
        else:
            return jsonify({"error": "Failed to update wallet balance"}), 500

    except Exception as e:
        logger.error(f"Error updating wallet balance: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/mint/<address>/<int:balance>', methods=['GET'])
def add_wallet_balance(address: str, balance: int):
    """Add balance to wallet (mint tokens)"""
    try:
        new_balance = db.increment_wallet_balance(address, float(balance))
        logs.append(f"Minted {balance} tokens for {address}")
        return Response(format_balance(new_balance), mimetype='text/plain')

    except Exception as e:
        logger.error(f"Error adding wallet balance: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/wallet/<address>/last_tx', methods=['GET'])
def get_wallet_last_transaction(address: str):
    """Get last transaction for wallet"""
    last_tx = db.get_wallet_last_transaction(address)
    return Response(last_tx or '', mimetype='text/plain')

# Chunk Routes
@app.route('/chunk', methods=['POST'])
def post_chunk():
    """Submit chunk data"""
    try:
        data = request.get_json()
        if not data or 'chunk' not in data:
            return jsonify({"status": 400, "error": "Invalid chunk data"}), 400

        # Validate required fields for chunk
        required_chunk_fields = ['chunk', 'data_root', 'data_size']
        for field in required_chunk_fields:
            if field not in data:
                return jsonify({"status": 400, "error": f"Missing required chunk field: {field}"}), 400

        # Calculate offset - in the original arlocal, this is calculated based on existing chunks
        # For this implementation, we'll use a simpler approach
        offset = db.get_next_chunk_offset()

        # Store chunk
        chunk_data = {
            'chunk': data['chunk'],
            'data_root': data['data_root'],
            'data_size': int(data['data_size']),
            'offset': offset,
            'data_path': data.get('data_path', '')
        }

        if db.insert_chunk(chunk_data):
            logs.append(f"Chunk stored for data_root {data['data_root']} at offset: {offset}")
            return jsonify({})
        else:
            return jsonify({"error": "Failed to store chunk"}), 500

    except Exception as e:
        logger.error(f"Error posting chunk: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/chunk/<int:offset>', methods=['GET'])
def get_chunk_by_offset_route(offset: int):
    """Get chunk by offset"""
    chunk = db.get_chunk_by_offset(offset)
    if not chunk:
        return '', 204  # No Content

    return jsonify(chunk)

# Data Routes (for transaction data access)
@app.route('/<txid>', methods=['GET', 'HEAD'])
@app.route('/<txid>/<path:subpath>', methods=['GET', 'HEAD'])
def get_data_route(txid: str, subpath: str = None):
    """Get transaction data by transaction ID"""
    if not validate_txid(txid):
        return jsonify({"status": 404, "error": "Not Found"}), 404

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found"}), 404

    if request.method == 'HEAD':
        # Return headers only
        response = Response()
        response.headers['Content-Length'] = str(transaction.get('data_size', 0))

        # Check if data exists (either as file or reconstructable from chunks)
        data = get_transaction_data(transaction, txid)
        if data is not None:
            response.headers['Content-Type'] = 'application/octet-stream'
        return response

    # GET request - return data
    data = get_transaction_data(transaction, txid)
    if data is not None:
        # Determine content type from transaction tags
        content_type = 'application/octet-stream'
        if 'tags' in transaction and transaction['tags']:
            tags = transaction['tags']
            for tag in tags:
                try:
                    # Decode tag name and value from base64url
                    tag_name = b64url_to_bytes(tag.get('name', '')).decode('utf-8')
                    tag_value = b64url_to_bytes(tag.get('value', '')).decode('utf-8')
                    if tag_name.lower() == 'content-type':
                        content_type = tag_value
                        break
                except Exception:
                    # If decoding fails, try direct comparison (fallback)
                    if tag.get('name') == 'Content-Type':
                        content_type = tag.get('value', content_type)
                        break

        # Ensure data is bytes for binary response
        if isinstance(data, str):
            # If data is a string (base64url), decode it
            try:
                data = b64url_to_bytes(data)
            except Exception as e:
                logger.error(f"Error decoding data string: {e}")
                data = data.encode('utf-8')  # Fallback to UTF-8 encoding

        return Response(data, mimetype=content_type)

    return jsonify({"status": 404, "error": "Data not found"}), 404


def get_transaction_data(transaction: dict, txid: str) -> bytes:
    """Get transaction data, either from file or by reconstructing from chunks"""
    # First try to load data from file (for transactions with embedded data)
    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        # Ensure we return bytes
        if isinstance(data, str):
            # If it's a string, try to decode as base64url first, then fallback to UTF-8
            try:
                return b64url_to_bytes(data)
            except Exception:
                return data.encode('utf-8')
        return data

    # If no file data, try to reconstruct from chunks using data_root
    data_root = transaction.get('data_root', '')
    data_size = int(transaction.get('data_size', 0))

    if data_root and data_size > 0:
        # Get chunks for this data_root
        chunks = db.get_chunks_by_data_root(data_root)
        if chunks:
            # Sort chunks by offset and reconstruct data
            chunks.sort(key=lambda x: x.get('offset', 0))
            reconstructed_data = b''

            for chunk in chunks:
                try:
                    # Decode chunk data from base64url
                    chunk_data = b64url_to_bytes(chunk['chunk'])
                    reconstructed_data += chunk_data
                except Exception as e:
                    logger.error(f"Error decoding chunk: {e}")
                    continue

            # Verify reconstructed data size matches expected
            if len(reconstructed_data) == data_size:
                return reconstructed_data
            else:
                logger.warning(f"Reconstructed data size {len(reconstructed_data)} doesn't match expected {data_size}")

    return None

# GraphQL endpoint
@app.route('/graphql', methods=['POST', 'GET'])
def graphql_endpoint():
    """GraphQL endpoint with full Arweave API compatibility"""
    if request.method == 'GET':
        # Return GraphQL playground/introspection info
        return jsonify({
            "message": "ArLocal GraphQL endpoint",
            "available_queries": ["transaction", "transactions", "block", "blocks"],
            "schema_url": "/graphql",
            "playground": "Send POST requests with GraphQL queries"
        })

    # POST request - handle GraphQL queries
    try:
        data = request.get_json()
        if not data:
            return jsonify({"errors": [{"message": "No JSON data provided"}]}), 400

        query = data.get('query', '')
        variables = data.get('variables', {})
        operation_name = data.get('operationName')

        if not query:
            return jsonify({"errors": [{"message": "No query provided"}]}), 400

        # Execute GraphQL query
        context = {
            'db': db,
            'request': request
        }

        result = schema.execute(
            query,
            variables=variables,
            context=context,
            operation_name=operation_name
        )

        # Format response
        response_data = {}
        if result.data:
            response_data['data'] = result.data

        if result.errors:
            response_data['errors'] = [
                {"message": str(error), "locations": getattr(error, 'locations', None)}
                for error in result.errors
            ]

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"GraphQL execution error: {e}")
        return jsonify({"errors": [{"message": f"Internal server error: {str(e)}"}]}), 500

# Catch-all route for unmatched requests
@app.route('/<path:other>', methods=['GET'])
def catch_all(other: str):
    """Catch-all route for unmatched requests"""
    return jsonify({
        "status": 400,
        "error": "Request type not found."
    }), 400

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"status": 404, "error": "Not Found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"status": 500, "error": "Internal Server Error"}), 500

if __name__ == '__main__':
    print(f"Starting ArLocal Python server on {app_config.HOST}:{app_config.PORT}...")
    print(f"Network: {app_config.NETWORK_NAME}")
    print(f"Debug mode: {app_config.DEBUG}")
    print("Available endpoints:")
    print("  GET  /                   - Network status")
    print("  GET  /info               - Network info")
    print("  GET  /peers              - Peer list")
    print("  GET  /logs               - Application logs")
    print("  GET  /reset              - Reset network")
    print("  GET  /tx_anchor          - Transaction anchor")
    print("  GET  /price/<bytes>      - Calculate price")
    print("  GET  /tx/pending         - Pending transactions")
    print("  GET  /tx/<txid>          - Get transaction")
    print("  POST /tx                 - Submit transaction")
    print("  GET  /mine/<qty>         - Mine blocks")
    print("  POST /wallet             - Create wallet")
    print("  GET  /wallet/<addr>/balance - Get balance")
    print("  GET  /mint/<addr>/<amt>  - Mint tokens")
    print("  POST /chunk              - Submit chunk")
    print("  GET  /chunk/<offset>     - Get chunk")
    print("  POST /graphql            - GraphQL endpoint (full Arweave API)")
    print("  GET  /graphql            - GraphQL schema info")
    print()

    app.run(host=app_config.HOST, port=app_config.PORT, debug=app_config.DEBUG)
